{% extends 'base.html' %}

{% block title %}Conversation with {{ other_user.username }} | PetPaw{% endblock %}

{% block extra_css %}
<style>
    /* Story comment message styles */
    .story-comment-message {
        display: flex;
        gap: var(--gap-sm);
        align-items: flex-start;
    }

    .story-thumbnail {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-base);
        overflow: hidden;
        flex-shrink: 0;
    }

    .story-thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .story-thumbnail-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--primary-light), var(--primary));
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--font-lg);
    }

    .story-comment-content {
        flex: 1;
        min-width: 0;
    }

    .story-comment-label {
        font-size: var(--font-xs);
        color: var(--text-light);
        margin-bottom: var(--spacing-xs);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .story-comment-text {
        font-weight: var(--fw-medium);
        word-wrap: break-word;
    }
    .conversation-container {
        display: grid;
        grid-template-columns: 350px 1fr;
        height: calc(100vh - 200px);
        min-height: 600px;
        max-height: 800px;
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
        position: relative;
    }

    @media (max-width: 992px) {
        .conversation-container {
            grid-template-columns: 1fr;
        }

        .conversation-list {
            display: none;
        }
    }

    .conversation-list {
        border-right: 1px solid var(--gray-200);
        overflow-y: auto;
    }

    .conversation-list-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .conversation-list-title {
        font-size: var(--font-lg);
        margin: 0;
    }

    .new-message-button {
        background: none;
        border: none;
        color: var(--primary);
        cursor: pointer;
        font-size: var(--font-xl);
    }

    .search-conversations {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-sm);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .conversation-items {
        padding: var(--spacing-base) 0;
    }

    .conversation-item {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
        padding: var(--spacing-base) var(--spacing-xl);
        cursor: pointer;
        transition: var(--transition-base);
        text-decoration: none;
        color: inherit;
    }

    .conversation-item:hover {
        background-color: var(--gray-100);
        text-decoration: none;
        color: inherit;
    }

    .conversation-item.active {
        background-color: var(--primary-light);
    }

    .conversation-avatar {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .conversation-info {
        flex: 1;
        min-width: 0;
    }

    .conversation-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xs);
    }

    .conversation-name {
        font-weight: var(--fw-medium);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        white-space: nowrap;
    }

    .conversation-preview {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-sm);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .conversation-status {
        position: relative;
    }

    .unread-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: var(--primary);
        color: var(--white);
        font-size: 10px;
        width: 18px;
        height: 18px;
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .conversation-detail {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .conversation-detail-header {
        padding: var(--spacing-base) var(--spacing-xl);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }

    .conversation-detail-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        object-fit: cover;
    }

    .conversation-detail-info {
        flex: 1;
    }

    .conversation-detail-name {
        font-weight: var(--fw-medium);
    }

    .conversation-detail-status {
        font-size: var(--font-xs);
        color: var(--text-light);
    }

    .conversation-detail-actions {
        display: flex;
        gap: var(--gap-sm);
    }

    .conversation-action-button {
        background: none;
        border: none;
        color: var(--text-light);
        cursor: pointer;
        font-size: var(--font-lg);
        transition: var(--transition-base);
    }

    .conversation-action-button:hover {
        color: var(--primary);
    }

    .chat-messages-container {
        flex: 1;
        padding: var(--spacing-xl);
        overflow-y: auto;
        overflow-x: hidden;
        display: block;
        height: 100%;
        max-height: none;
        min-height: 0;
        position: relative;
    }

    .chat-messages-container::after {
        content: "";
        display: table;
        clear: both;
    }

    .message {
        display: flex;
        max-width: 70%;
        width: fit-content;
        margin-bottom: var(--spacing-base);
        align-items: flex-end;
        position: relative;
        clear: both;
    }

    .message.incoming {
        align-self: flex-start;
        flex-direction: row;
        float: left;
        clear: left;
    }

    .message.outgoing {
        align-self: flex-end;
        flex-direction: row-reverse;
        float: right;
        clear: right;
    }

    .message-avatar {
        width: 36px;
        height: 36px;
        border-radius: var(--radius-full);
        object-fit: cover;
        margin: 0 var(--spacing-sm);
        flex-shrink: 0;
    }

    .message.incoming .message-avatar {
        margin-right: var(--spacing-sm);
        margin-left: 0;
    }

    .message.outgoing .message-avatar {
        margin-left: var(--spacing-sm);
        margin-right: 0;
    }

    .message-content {
        background-color: var(--gray-100);
        padding: var(--spacing-sm) var(--spacing-base);
        border-radius: var(--radius-lg);
        position: relative;
        max-width: 100%;
        word-wrap: break-word;
        word-break: break-word;
        overflow-wrap: break-word;
        flex: 1;
        box-sizing: border-box;
    }

    .message.outgoing .message-content {
        background-color: var(--primary-light);
        color: var(--primary-dark);
    }

    .message.incoming .message-content {
        border-bottom-left-radius: var(--spacing-xs);
    }

    .message.outgoing .message-content {
        border-bottom-right-radius: var(--spacing-xs);
    }

    .message-text {
        margin-bottom: var(--spacing-xs);
        word-wrap: break-word;
    }

    .message-time {
        font-size: var(--font-xs);
        color: var(--text-light);
        text-align: right;
    }

    .message-form-container {
        padding: var(--spacing-base) var(--spacing-xl);
        border-top: 1px solid var(--gray-200);
    }

    .message-form {
        display: flex;
        align-items: center;
        gap: var(--gap-base);
    }

    .message-input {
        flex: 1;
        padding: var(--spacing-sm) var(--spacing-base);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        font-size: var(--font-base);
        resize: none;
    }

    .message-input:focus {
        outline: none;
        border-color: var(--primary);
    }

    .message-submit {
        background-color: var(--primary);
        color: var(--white);
        border: none;
        border-radius: var(--radius-full);
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-base);
    }

    .message-submit:hover {
        background-color: var(--primary-dark);
    }

    .message-submit:disabled {
        background-color: var(--gray-300);
        cursor: not-allowed;
    }

    .typing-indicator {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
        color: var(--text-light);
        font-size: var(--font-xs);
        padding: var(--spacing-xs) 0;
    }

    .typing-dots {
        display: flex;
        gap: 2px;
    }

    .typing-dot {
        width: 6px;
        height: 6px;
        border-radius: var(--radius-full);
        background-color: var(--text-light);
        animation: typingAnimation 1.5s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) {
        animation-delay: 0s;
    }

    .typing-dot:nth-child(2) {
        animation-delay: 0.3s;
    }

    .typing-dot:nth-child(3) {
        animation-delay: 0.6s;
    }

    @keyframes typingAnimation {
        0% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-5px);
        }
        100% {
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="conversation-container">
        <div class="conversation-list">
            <div class="conversation-list-header">
                <h2 class="conversation-list-title">Messages</h2>
                <button type="button" class="new-message-button" id="new-message-btn">
                    <i class="fas fa-edit"></i>
                </button>
            </div>

            <div class="search-conversations">
                <input type="text" placeholder="Search conversations..." class="search-input" id="search-conversations">
            </div>

            <div class="conversation-items">
                {% for conv in conversations %}
                    <a href="{% url 'conversation-detail' pk=conv.id %}" class="conversation-item {% if conversation.id == conv.id %}active{% endif %}">
                        {% if conv.other_user.profile_picture %}
                            <img src="{{ conv.other_user.profile_picture.url }}" alt="{{ conv.other_user.username }}" class="conversation-avatar">
                        {% else %}
                            <img src="/static/img/default-avatar.svg" alt="{{ conv.other_user.username }}" class="conversation-avatar">
                        {% endif %}

                        <div class="conversation-info">
                            <div class="conversation-header">
                                <div class="conversation-name">{{ conv.other_user.username }}</div>
                                {% if conv.last_message %}
                                    <div class="conversation-time">{{ conv.last_message.created_at|date:"g:i A" }}</div>
                                {% endif %}
                            </div>

                            {% if conv.last_message %}
                                <div class="conversation-preview">
                                    {% if conv.last_message.sender == user %}
                                        <span>You:</span>
                                    {% endif %}
                                    {{ conv.last_message.content|truncatechars:30 }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="conversation-status">
                            {% if conv.unread_count > 0 and conv.last_message.sender != user %}
                                <div class="unread-badge">{{ conv.unread_count }}</div>
                            {% endif %}
                        </div>
                    </a>
                {% endfor %}
            </div>
        </div>

        <div class="conversation-detail">
            <div class="conversation-detail-header">
                {% if other_user.profile_picture %}
                    <img src="{{ other_user.profile_picture.url }}" alt="{{ other_user.username }}" class="conversation-detail-avatar">
                {% else %}
                    <img src="/static/img/default-avatar.svg" alt="{{ other_user.username }}" class="conversation-detail-avatar">
                {% endif %}

                <div class="conversation-detail-info">
                    <div class="conversation-detail-name">{{ other_user.username }}</div>
                    <div class="conversation-detail-status">
                        {% if other_user.is_online %}
                            <span class="text-success">Online</span>
                        {% else %}
                            <span>Last seen {{ other_user.last_login|timesince }} ago</span>
                        {% endif %}
                    </div>
                </div>

                <div class="conversation-detail-actions">
                    <a href="{% url 'user-profile' username=other_user.username %}" class="conversation-action-button" title="View Profile">
                        <i class="fas fa-user"></i>
                    </a>
                    <button type="button" class="conversation-action-button" title="More Options">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>

            <div class="chat-messages-container" id="chat-messages-container">
                {% for message in messages %}
                    <div class="message {% if message.sender == user %}outgoing{% else %}incoming{% endif %}">
                        {% if message.sender != user %}
                            {% if message.sender.profile_picture %}
                                <img src="{{ message.sender.profile_picture.url }}" alt="{{ message.sender.username }}" class="message-avatar">
                            {% else %}
                                <img src="/static/img/default-avatar.svg" alt="{{ message.sender.username }}" class="message-avatar">
                            {% endif %}
                        {% endif %}

                        <div class="message-content">
                            {% if message.story %}
                                <!-- Story comment message with thumbnail -->
                                <div class="story-comment-message">
                                    <div class="story-thumbnail">
                                        {% if message.story.image %}
                                            <img src="{{ message.story.image.url }}" alt="Story">
                                        {% elif message.story.video %}
                                            <video src="{{ message.story.video.url }}" muted>
                                                <div class="story-thumbnail-placeholder">
                                                    <i class="fas fa-play"></i>
                                                </div>
                                            </video>
                                        {% else %}
                                            <div class="story-thumbnail-placeholder">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="story-comment-content">
                                        <div class="story-comment-label">Commented on your story</div>
                                        <div class="story-comment-text">{{ message.content|slice:"20:" }}</div>
                                    </div>
                                </div>
                            {% else %}
                                <div class="message-text">{{ message.content }}</div>
                                {% if message.image %}
                                    <div class="message-image">
                                        <img src="{{ message.image.url }}" alt="Message image" style="max-width: 200px; border-radius: var(--radius-base); margin-top: var(--spacing-xs);">
                                    </div>
                                {% endif %}
                            {% endif %}
                            <div class="message-time">{{ message.created_at|date:"g:i A" }}</div>
                        </div>
                    </div>
                {% empty %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-comments fa-3x mb-3"></i>
                        <p>No messages yet. Start the conversation!</p>
                    </div>
                {% endfor %}

                <div class="typing-indicator" id="typing-indicator" style="display: none;">
                    <span>{{ other_user.username }} is typing</span>
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>

            <div class="message-form-container">
                <form method="post" action="{% url 'conversation-detail' pk=conversation.id %}" class="message-form" id="message-form" enctype="multipart/form-data">
                    {% csrf_token %}
                    <textarea name="content" id="message-input" class="message-input" placeholder="Type a message..." rows="1" required></textarea>
                    <input type="file" name="image" id="image-input" accept="image/*" style="display: none;">
                    <button type="button" class="conversation-action-button" onclick="document.getElementById('image-input').click();" title="Attach Image">
                        <i class="fas fa-image"></i>
                    </button>
                    <button type="submit" class="message-submit" id="message-submit">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Completely disable browser notifications
if ('Notification' in window) {
    // Override the Notification constructor to prevent any notifications
    window.Notification = function() {
        console.log('Browser notification blocked');
        return {
            close: function() {},
            onclick: null,
            onclose: null,
            onerror: null,
            onshow: null
        };
    };
    window.Notification.permission = 'denied';
    window.Notification.requestPermission = function() {
        return Promise.resolve('denied');
    };
}

document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById('chat-messages-container');
    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    const messageSubmit = document.getElementById('message-submit');
    const typingIndicator = document.getElementById('typing-indicator');

    // Auto-scroll to bottom of messages
    function scrollToBottom() {
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    // Initial scroll to bottom
    scrollToBottom();

    // No browser notifications - notifications will only appear in notifications tab

    // Auto-resize textarea
    messageInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });

    // Handle form submission
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const content = formData.get('content').trim();

        if (!content) return;

        // Disable submit button
        messageSubmit.disabled = true;

        // Send message via AJAX
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': formData.get('csrfmiddlewaretoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Add message to UI
                const messageHtml = `
                    <div class="message outgoing">
                        <div class="message-content">
                            <div class="message-text">${data.message.content}</div>
                            ${data.message.image_url ? `<div class="message-image"><img src="${data.message.image_url}" alt="Message image" style="max-width: 200px; border-radius: var(--radius-base);"></div>` : ''}
                            <div class="message-time">${data.message.created_at}</div>
                        </div>
                    </div>
                `;
                messagesContainer.insertAdjacentHTML('beforeend', messageHtml);

                // Clear form and scroll to bottom
                messageInput.value = '';
                messageInput.style.height = 'auto';
                document.getElementById('image-input').value = '';
                scrollToBottom();
            }
        })
        .catch(error => {
            console.error('Error sending message:', error);
        })
        .finally(() => {
            messageSubmit.disabled = false;
        });
    });

    // WebSocket connection for real-time messaging
    const conversationId = {{ conversation.id }};
    const ws_scheme = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const ws_path = `${ws_scheme}://${window.location.host}/ws/chat/${conversationId}/`;

    // Only try WebSocket if the browser supports it
    if ('WebSocket' in window) {
        try {
            const socket = new WebSocket(ws_path);

            socket.onmessage = function(e) {
            const data = JSON.parse(e.data);

            if (data.type === 'message' && !data.is_self) {
                // Add incoming message to UI
                const messageHtml = `
                    <div class="message incoming">
                        <img src="/static/img/default-avatar.svg" alt="${data.sender_username}" class="message-avatar">
                        <div class="message-content">
                            <div class="message-text">${data.message}</div>
                            <div class="message-time">${new Date(data.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                        </div>
                    </div>
                `;
                messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
                scrollToBottom();

                // No browser notifications - notifications are handled in the backend
            } else if (data.type === 'message' && data.is_self) {
                // Handle own messages sent from other tabs/devices
                const messageHtml = `
                    <div class="message outgoing">
                        <div class="message-content">
                            <div class="message-text">${data.message}</div>
                            <div class="message-time">${new Date(data.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                        </div>
                    </div>
                `;
                messagesContainer.insertAdjacentHTML('beforeend', messageHtml);
                scrollToBottom();
            } else if (data.type === 'typing') {
                // Show typing indicator
                typingIndicator.style.display = 'flex';
                setTimeout(() => {
                    typingIndicator.style.display = 'none';
                }, 3000);
            }
        };

        socket.onopen = function(e) {
            console.log('WebSocket connection established');
        };

        socket.onclose = function(e) {
            console.log('WebSocket connection closed');
            // Only try to reconnect if it wasn't a normal closure
            if (e.code !== 1000) {
                console.log('Unexpected WebSocket closure, will try to reconnect...');
                // Don't reload the page, just log the issue
                // In production, you might want to show a "Connection lost" message
            }
        };

        socket.onerror = function(e) {
            console.error('WebSocket error:', e);
        };

        // Send typing indicator
        let typingTimer;
        messageInput.addEventListener('input', function() {
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    'type': 'typing'
                }));
            }
        });

        } catch (error) {
            console.log('WebSocket connection failed:', error);
        }
    } else {
        console.log('WebSocket not supported by this browser');
    }
});
</script>
{% endblock %}
