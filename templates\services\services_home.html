{% extends 'base.html' %}
{% load static %}

{% block title %}Pet Services - PetPaw{% endblock %}

{% block content %}
<div class="container">
    <!-- Hero Section -->
    <div class="services-hero">
        <div class="hero-content">
            <h1>Professional Pet Services</h1>
            <p>Find trusted pet care professionals in your area. From grooming to training, we have the right service for your furry friend.</p>
            <div class="hero-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ total_providers }}</span>
                    <span class="stat-label">Service Providers</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ total_categories }}</span>
                    <span class="stat-label">Service Categories</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label">Happy Pets</span>
                </div>
            </div>
            <div class="hero-actions">
                <a href="{% url 'provider-list' %}" class="btn btn-primary btn-lg">
                    <i class="fas mr-sm fa-search"></i>
                    Browse All Providers
                </a>
                <a href="{% url 'become-provider' %}" class="btn btn-outline btn-outline-white btn-lg">
                    <i class="fas mr-sm fa-user-plus"></i>
                    Become a Providers
                </a>
            </div>
        </div>
    </div>

    <!-- Service Categories -->
    <section class="categories-section">
        <div class="section-header">
            <h2>Service Categories</h2>
            <p>Choose from our wide range of professional pet services</p>
        </div>

        <!-- Quick Filters -->
        <div class="quick-filters">
            <div class="filter-group">
                <label>Filter by Service:</label>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-category="all">All Services</button>
                    {% for category in categories %}
                        <button class="filter-btn" data-category="{{ category.slug }}">{{ category.name }}</button>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="categories-grid" id="categoriesGrid">
            {% for category in categories %}
                <div class="category-card modern-card" data-category="{{ category.slug }}" onclick="window.location.href='{% url 'provider-list' %}?category={{ category.slug }}'">
                    <div class="card-header">
                        <div class="category-icon">
                            {% if category.icon %}
                                <img src="{{ category.icon.url }}" alt="{{ category.name }}">
                            {% elif category.icon_class %}
                                <i class="{{ category.icon_class }}"></i>
                            {% else %}
                                <i class="fas fa-paw"></i>
                            {% endif %}
                        </div>
                        <div class="category-badge">
                            <span class="provider-count">{{ category.providers.count }}</span>
                            <span class="provider-label">Provider{{ category.providers.count|pluralize }}</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h3>{{ category.name }}</h3>
                        <p>{{ category.description|truncatewords:12|default:"Professional pet care services" }}</p>
                    </div>
                    <div class="card-footer">
                        <div class="category-stats">
                            <span class="stat-item">
                                <i class="fas fa-star"></i>
                                <span>4.8</span>
                            </span>
                            <span class="stat-item">
                                <i class="fas fa-clock"></i>
                                <span>Quick</span>
                            </span>
                        </div>
                        <div class="card-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <div class="section-footer">
            <a href="{% url 'service-categories' %}" class="btn btn-outline-primary">
                View All Categories
            </a>
        </div>
    </section>

    <!-- Featured Providers -->
    {% if featured_providers %}
    <section class="featured-providers-section">
        <div class="section-header">
            <h2>Featured Service Providers</h2>
            <p>Top-rated professionals ready to care for your pets</p>
        </div>

        <div class="providers-grid">
            {% for provider in featured_providers %}
                <div class="provider-card">
                    <div class="provider-image">
                        {% if provider.profile_picture %}
                            <img src="{{ provider.profile_picture.url }}" alt="{{ provider.user.get_full_name|default:provider.user.username }}">
                        {% else %}
                            <div class="provider-placeholder">
                                <i class="fas fa-user"></i>
                            </div>
                        {% endif %}
                    </div>

                    <div class="provider-content">
                        <h3>{{ provider.user.get_full_name|default:provider.user.username }}</h3>

                        <div class="provider-rating">
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= provider.rating %}
                                        <i class="fas fa-star"></i>
                                    {% elif forloop.counter <= provider.rating|add:0.5 %}
                                        <i class="fas fa-star-half-alt"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="rating-text">{{ provider.rating|floatformat:1 }} ({{ provider.reviews_count }})</span>
                        </div>

                        <div class="provider-categories">
                            {% for category in provider.categories.all|slice:":2" %}
                                <span class="category-tag">{{ category.name }}</span>
                            {% endfor %}
                            {% if provider.categories.count > 2 %}
                                <span class="category-tag more">+{{ provider.categories.count|add:"-2" }}</span>
                            {% endif %}
                        </div>

                        <p class="provider-bio">{{ provider.bio|truncatewords:15 }}</p>

                        <div class="provider-meta">
                            <span class="experience">
                                <i class="fas fa-clock"></i>
                                {{ provider.experience_years }} years
                            </span>
                            <span class="rate">
                                <i class="fas fa-dollar-sign"></i>
                                ${{ provider.hourly_rate }}/hour
                            </span>
                        </div>

                        <a href="{% url 'provider-detail' provider.pk %}" class="btn btn-primary">
                            View Profile
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>

        <div class="section-footer">
            <a href="{% url 'provider-list' %}" class="btn btn-outline-primary">
                View All Providers
            </a>
        </div>
    </section>
    {% endif %}

    <!-- Call to Action -->
    <section class="cta-section">
        <div class="cta-content">
            <h2>Ready to Get Started?</h2>
            <p>Join thousands of pet owners who trust our platform for their pet care needs.</p>
            <div class="cta-actions">
                <a href="{% url 'provider-list' %}" class="btn btn-primary btn-lg">
                    Find a Service Provider
                </a>
                <a href="{% url 'become-provider' %}" class="btn btn-outline-light btn-lg">
                    Become a Provider
                </a>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
.services-hero {
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: var(--white);
    padding: var(--spacing-4xl) 0;
    margin-bottom: var(--spacing-4xl);
    border-radius: var(--radius-lg);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.services-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.hero-content {
    position: relative;
    z-index: 1;
}

.services-hero h1 {
    font-size: var(--font-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-base);
}

.services-hero p {
    font-size: var(--font-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-sm);
    opacity: 0.8;
}

.hero-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-base);
    flex-wrap: wrap;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-header h2 {
    font-size: var(--font-3xl);
    margin-bottom: var(--spacing-sm);
    color: var(--gray-800);
}

.section-header p {
    color: var(--gray-600);
    font-size: var(--font-lg);
}

.categories-section {
    margin-bottom: var(--spacing-4xl);
}

.quick-filters {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.filter-group label {
    display: block;
    margin-bottom: var(--spacing-base);
    font-weight: 600;
    color: var(--gray-700);
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-sm);
}

.filter-btn {
    padding: var(--spacing-sm) var(--spacing-base);
    border: 2px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-full);
    font-size: var(--font-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-base);
}

.filter-btn:hover {
    border-color: var(--primary);
    color: var(--primary);
}

.filter-btn.active {
    background: var(--primary);
    border-color: var(--primary);
    color: var(--white);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.category-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition-base);
    cursor: pointer;
    overflow: hidden;
}

.category-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-8px);
    border-color: var(--primary);
}

.modern-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: var(--spacing-lg) var(--spacing-lg) 0;
}

.modern-card .card-body {
    padding: var(--spacing-base) var(--spacing-lg);
}

.modern-card .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-lg) var(--spacing-lg);
}

.category-badge {
        display: flex;
    background: var(--primary-light);
    border-radius: var(--radius-full);
    padding: var(--spacing-sm) var(--spacing-lg);
    text-align: center;
    min-width: 60px;
    gap: var(--gap-sm);
}

.category-badge .provider-count {
    display: block;
    font-weight: 700;
    color: var(--primary);
    font-size: var(--font-sm);
    line-height: 1;
}

.category-badge .provider-label {
    display: block;
    font-size: var(--font-xs);
    color: var(--primary);
    opacity: 0.8;
    line-height: 1;
    margin-top: 2px;
}

.category-stats {
    display: flex;
    gap: var(--spacing-base);
}

.category-stats .stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-sm);
    color: var(--gray-600);
}

.category-stats .stat-item i {
    color: var(--warning);
}

.card-arrow {
    color: var(--primary);
    font-size: var(--font-lg);
    transition: var(--transition-base);
}

.category-card:hover .card-arrow {
    transform: translateX(4px);
}

.category-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.category-icon i {
    font-size: 1.5rem;
    color: var(--primary);
}

.category-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

.category-info h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--gray-800);
}

.category-info p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-base);
    line-height: 1.6;
}

.category-meta {
    margin-bottom: var(--spacing-base);
}

.provider-count {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--gray-500);
    font-size: var(--font-sm);
}

.featured-providers-section {
    margin-bottom: var(--spacing-4xl);
}

.providers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.provider-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition-base);
}

.provider-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-4px);
}

.provider-image {
    height: 200px;
    overflow: hidden;
}

.provider-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.provider-placeholder {
    width: 100%;
    height: 100%;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
}

.provider-placeholder i {
    font-size: 3rem;
    color: var(--gray-400);
}

.provider-content {
    padding: var(--spacing-lg);
}

.provider-content h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--gray-800);
}

.provider-rating {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-base);
}

.rating-stars {
    display: flex;
    gap: 2px;
}

.rating-stars i {
    color: var(--warning);
    font-size: var(--font-sm);
}

.rating-text {
    font-size: var(--font-sm);
    color: var(--gray-600);
}

.provider-categories {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-base);
}

.category-tag {
    background: var(--primary-light);
    color: var(--primary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-full);
    font-size: var(--font-xs);
    font-weight: 500;
}

.category-tag.more {
    background: var(--gray-100);
    color: var(--gray-600);
}

.provider-bio {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-base);
    font-size: var(--font-sm);
}

.provider-meta {
    display: flex;
    gap: var(--spacing-base);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-sm);
    color: var(--gray-600);
}

.provider-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.provider-meta i {
    color: var(--primary);
}

.section-footer {
    text-align: center;
}

.cta-section {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary));
    color: var(--white);
    padding: var(--spacing-4xl) 0;
    border-radius: var(--radius-lg);
    text-align: center;
    margin-top: var(--spacing-4xl);
}

.cta-content h2 {
    font-size: var(--font-3xl);
    margin-bottom: var(--spacing-base);
}

.cta-content p {
    font-size: var(--font-lg);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-base);
    flex-wrap: wrap;
}

.mr-sm {
    margin-right: var(--spacing-sm);
}

@media (max-width: 768px) {
    .services-hero {
        padding: var(--spacing-2xl) 0;
    }

    .services-hero h1 {
        font-size: var(--font-2xl);
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--spacing-base);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .categories-grid,
    .providers-grid {
        grid-template-columns: 1fr;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .category-card,
    .provider-content {
        padding: var(--spacing-base);
    }

    .category-icon {
        width: 60px;
        height: 60px;
    }

    .category-icon i {
        font-size: 1.5rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const categoryCards = document.querySelectorAll('.category-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');

            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter cards
            categoryCards.forEach(card => {
                const cardCategory = card.getAttribute('data-category');

                if (category === 'all' || cardCategory === category) {
                    card.style.display = 'block';
                    card.style.animation = 'fadeIn 0.3s ease-in-out';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });

    // Add fade-in animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
