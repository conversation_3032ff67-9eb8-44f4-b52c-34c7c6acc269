{% extends 'base.html' %}

{% block title %}Create New Story | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .story-form-container {
        max-width: 600px;
        margin: 0 auto;
        padding: var(--spacing-xl) 0;
    }

    .story-form-header {
        margin-bottom: var(--spacing-2xl);
        text-align: center;
    }

    .story-form {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-2xl);
    }

    .form-section {
        margin-bottom: var(--spacing-2xl);
    }

    .form-section-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-lg);
        padding-bottom: var(--spacing-sm);
        border-bottom: 1px solid var(--gray-200);
    }

    .story-type-options {
        display: flex;
        gap: var(--gap-lg);
        margin-bottom: var(--spacing-lg);
    }

    .story-type-option {
        flex: 1;
        text-align: center;
        padding: var(--spacing-base);
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-md);
        cursor: pointer;
        transition: var(--transition-base);
    }

    .story-type-option.active {
        border-color: var(--primary);
        background-color: rgba(var(--primary-rgb), 0.05);
    }

    .story-type-option i {
        font-size: var(--font-2xl);
        margin-bottom: var(--spacing-sm);
        color: var(--text-light);
    }

    .story-type-option.active i {
        color: var(--primary);
    }

    .story-type-option span {
        display: block;
        font-weight: var(--fw-medium);
    }

    .file-upload-container {
        border: 2px dashed var(--gray-300);
        border-radius: var(--radius-md);
        padding: var(--spacing-xl);
        text-align: center;
        margin-bottom: var(--spacing-lg);
        cursor: pointer;
        transition: var(--transition-base);
    }

    .file-upload-container:hover {
        border-color: var(--primary);
    }

    .file-upload-icon {
        font-size: var(--font-3xl);
        color: var(--gray-400);
        margin-bottom: var(--spacing-base);
    }

    .file-upload-text {
        margin-bottom: var(--spacing-sm);
    }

    .file-upload-info {
        font-size: var(--font-sm);
        color: var(--text-light);
    }

    .preview-container {
        margin-top: var(--spacing-lg);
        text-align: center;
        display: none;
    }

    .preview-container img {
        max-width: 100%;
        max-height: 300px;
        border-radius: var(--radius-md);
    }

    .preview-container video {
        max-width: 100%;
        max-height: 300px;
        border-radius: var(--radius-md);
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: var(--spacing-r);
        margin-top: var(--spacing-xl);
    }

    @media (max-width: 768px) {
        .story-type-options {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="story-form-container">
        <div class="story-form-header">
            <h1>Create New Story</h1>
            <p>Share a moment that disappears in 24 hours</p>
        </div>

        <div class="story-form">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <div class="form-section">
                    <h2 class="form-section-title">Story Type</h2>

                    <div class="story-type-options">
                        <div class="story-type-option active" data-type="image">
                            <i class="far fa-image"></i>
                            <span>Image</span>
                        </div>
                        <div class="story-type-option" data-type="video">
                            <i class="far fa-video"></i>
                            <span>Video</span>
                        </div>
                    </div>
                </div>

                <div class="form-section image-section">
                    <h2 class="form-section-title">Add Image</h2>

                    <div class="file-upload-container" id="image-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-image"></i>
                        </div>
                        <div class="file-upload-text">Click to upload an image</div>
                        <div class="file-upload-info">JPG, PNG or GIF • Max 5MB</div>
                        <div style="display:none;">{{ form.image }}</div>
                    </div>

                    <div class="preview-container" id="image-preview-container">
                        <img id="image-preview" src="#" alt="Preview">
                    </div>

                    {% if form.image.errors %}
                        <div class="form-text text-danger">{{ form.image.errors }}</div>
                    {% endif %}
                </div>

                <div class="form-section video-section" style="display: none;">
                    <h2 class="form-section-title">Add Video</h2>

                    <div class="file-upload-container" id="video-upload-container">
                        <div class="file-upload-icon">
                            <i class="far fa-video"></i>
                        </div>
                        <div class="file-upload-text">Click to upload a video</div>
                        <div class="file-upload-info">MP4, MOV or AVI • Max 15MB</div>
                        <div style="display:none;">{{ form.video }}</div>
                    </div>

                    <div class="preview-container" id="video-preview-container">
                        <video id="video-preview" src="#" controls></video>
                    </div>

                    {% if form.video.errors %}
                        <div class="form-text text-danger">{{ form.video.errors }}</div>
                    {% endif %}
                </div>

                <div class="form-section">
                    <h2 class="form-section-title">Caption</h2>

                    <div class="form-group">
                        <label for="{{ form.caption.id_for_label }}" class="form-label">Add a caption (optional)</label>
                        {{ form.caption }}
                        {% if form.caption.errors %}
                            <div class="form-text text-danger">{{ form.caption.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="form-section">
                    <h2 class="form-section-title">Tag a Pet (Optional)</h2>

                    <div class="form-group">
                        <label for="{{ form.pet.id_for_label }}" class="form-label">Select one of your pets</label>
                        {{ form.pet }}
                        {% if form.pet.errors %}
                            <div class="form-text text-danger">{{ form.pet.errors }}</div>
                        {% endif %}
                    </div>
                </div>

                <div class="form-actions">
                    <a href="{% url 'feed' %}" class="btn btn-outline">Cancel</a>
                    <button type="submit" class="btn btn-primary">Create Story</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Story type selection
        const storyTypeOptions = document.querySelectorAll('.story-type-option');
        const imageSection = document.querySelector('.image-section');
        const videoSection = document.querySelector('.video-section');

        storyTypeOptions.forEach(option => {
            option.addEventListener('click', function() {
                // Update active state
                storyTypeOptions.forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');

                // Show/hide relevant sections
                const type = this.getAttribute('data-type');
                if (type === 'image') {
                    imageSection.style.display = 'block';
                    videoSection.style.display = 'none';
                } else if (type === 'video') {
                    imageSection.style.display = 'none';
                    videoSection.style.display = 'block';
                }
            });
        });

        // Image upload preview
        const imageUploadContainer = document.getElementById('image-upload-container');
        const imageInput = document.querySelector('input[name="image"]');
        const imagePreviewContainer = document.getElementById('image-preview-container');
        const imagePreview = document.getElementById('image-preview');

        imageUploadContainer.addEventListener('click', function() {
            imageInput.click();
        });

        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreviewContainer.style.display = 'block';
                }

                reader.readAsDataURL(this.files[0]);
            }
        });

        // Video upload preview
        const videoUploadContainer = document.getElementById('video-upload-container');
        const videoInput = document.querySelector('input[name="video"]');
        const videoPreviewContainer = document.getElementById('video-preview-container');
        const videoPreview = document.getElementById('video-preview');

        videoUploadContainer.addEventListener('click', function() {
            videoInput.click();
        });

        videoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    videoPreview.src = e.target.result;
                    videoPreviewContainer.style.display = 'block';
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    });
</script>
{% endblock %}
