#!/usr/bin/env python
"""
Test script to create a story comment and verify the thumbnail display
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'petpaw.settings')
django.setup()

from django.contrib.auth import get_user_model
from social.models import Story, Notification
from messaging.models import Conversation, Message

User = get_user_model()

def test_story_comment_with_thumbnail():
    """Test story comment with thumbnail display"""
    
    # Get users
    try:
        user1 = User.objects.get(username='admin')  # Story owner
        user2 = User.objects.get(username='akash')  # Commenter
    except User.DoesNotExist:
        print("❌ Required users not found.")
        return
    
    # Get a story with an image
    story = Story.objects.filter(user=user1, image__isnull=False).first()
    if not story:
        print("❌ No stories with images found.")
        return
    
    print(f"✅ Testing with story {story.id} by {story.user.username}")
    print(f"   Story has image: {bool(story.image)}")
    if story.image:
        print(f"   Image URL: {story.image.url}")
    
    # Create story comment notification
    comment_text = "Amazing photo! Your pet looks so happy! 😊🐾"
    comment_notification = Notification.objects.create(
        recipient=story.user,
        sender=user2,
        notification_type='story_comment',
        story=story,
        story_comment_text=comment_text,
        message=f"{user2.username} commented on your story."
    )
    print(f"✅ Created story comment notification: {comment_notification.id}")
    
    # Get or create conversation
    conversation = Conversation.objects.filter(
        participants=user1
    ).filter(
        participants=user2
    ).first()
    
    if not conversation:
        conversation = Conversation.objects.create()
        conversation.participants.add(user1, user2)
        print(f"✅ Created new conversation: {conversation.id}")
    else:
        print(f"✅ Using existing conversation: {conversation.id}")
    
    # Create message with story reference
    message = Message.objects.create(
        conversation=conversation,
        sender=user2,
        content=f"Commented on your story: {comment_text}",
        story=story
    )
    print(f"✅ Created story comment message: {message.id}")
    print(f"   Message has story reference: {bool(message.story)}")
    print(f"   Story image URL: {message.story.image.url if message.story and message.story.image else 'No image'}")
    
    # Test URLs
    print(f"\n🎯 Test URLs:")
    print(f"   Story: http://127.0.0.1:8000/social/story/{story.id}/")
    print(f"   Notifications: http://127.0.0.1:8000/social/notifications/")
    print(f"   Messages: http://127.0.0.1:8000/messaging/conversation/{conversation.id}/")
    print(f"   Inbox: http://127.0.0.1:8000/messaging/")
    
    print(f"\n✅ Story comment with thumbnail test completed!")
    print(f"   The message should now show the story thumbnail in the conversation.")

if __name__ == "__main__":
    test_story_comment_with_thumbnail()
