from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import User, UserProfile, Address



class UserProfileAdmin(admin.ModelAdmin):
    """Admin configuration for UserProfile"""
    list_display = ('user', 'get_followers_count', 'get_following_count', 'website')
    list_filter = ('user__is_pet_owner', 'user__is_service_provider')
    search_fields = ('user__username', 'user__email', 'interests')
    readonly_fields = ('get_followers_count', 'get_following_count')

    def get_followers_count(self, obj):
        return obj.get_followers_count()
    get_followers_count.short_description = 'Followers'

    def get_following_count(self, obj):
        return obj.get_following_count()
    get_following_count.short_description = 'Following'


class AddressInline(admin.TabularInline):
    model = Address
    extra = 0


class CustomUserAdmin(UserAdmin):
    inlines = (AddressInline,)  # Remove UserProfileInline to avoid conflicts with signals
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff', 'is_pet_owner', 'is_service_provider')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'is_pet_owner', 'is_service_provider')
    fieldsets = UserAdmin.fieldsets + (
        ('Additional Info', {'fields': ('bio', 'location', 'birth_date', 'profile_picture',
                                       'is_pet_owner', 'is_service_provider', 'phone_number')}),
    )


admin.site.register(User, CustomUserAdmin)
admin.site.register(UserProfile, UserProfileAdmin)
admin.site.register(Address)
