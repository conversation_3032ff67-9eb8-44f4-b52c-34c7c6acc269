{% extends 'base.html' %}

{% block title %}Pets | PetPaw{% endblock %}

{% block extra_css %}
<style>
    .pet-list-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    .pet-list-title {
        margin-bottom: var(--spacing-base);
    }
    
    .pet-list-description {
        color: var(--text-light);
        max-width: 800px;
    }
    
    .pet-filters {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-2xl);
    }
    
    .filter-form {
        display: flex;
        flex-wrap: wrap;
        gap: var(--gap-lg);
    }
    
    .filter-group {
        flex: 1;
        min-width: 200px;
    }
    
    .filter-actions {
        display: flex;
        align-items: flex-end;
        gap: var(--gap-base);
    }
    
    .pet-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--gap-xl);
    }
    
    .pet-card {
        background-color: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-base);
        overflow: hidden;
        transition: var(--transition-base);
    }
    
    .pet-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-lg);
    }
    
    .pet-card-image {
        height: 200px;
        overflow: hidden;
        position: relative;
    }
    
    .pet-card-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-base);
    }
    
    .pet-card:hover .pet-card-image img {
        transform: scale(1.05);
    }
    
    .adoption-badge {
        position: absolute;
        top: var(--spacing-base);
        right: var(--spacing-base);
        background-color: var(--primary);
        color: var(--white);
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-full);
        font-size: var(--font-xs);
        font-weight: var(--fw-medium);
    }
    
    .pet-card-content {
        padding: var(--spacing-base) var(--spacing-xl);
    }
    
    .pet-card-title {
        font-size: var(--font-lg);
        margin-bottom: var(--spacing-xs);
    }
    
    .pet-card-breed {
        color: var(--text-light);
        margin-bottom: var(--spacing-sm);
        font-size: var(--font-sm);
    }
    
    .pet-card-info {
        display: flex;
        gap: var(--gap-base);
        margin-bottom: var(--spacing-2lg);
        font-size: var(--font-sm);
    }
    
    .pet-card-info-item {
        display: flex;
        align-items: center;
        gap: var(--gap-xs);
    }
    
    .pet-card-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .pet-card-price {
        font-weight: var(--fw-bold);
        color: var(--primary);
    }
    
    .pagination-container {
        margin-top: var(--spacing-3xl);
        display: flex;
        justify-content: center;
    }
    
    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        gap: var(--gap-xs);
    }
    
    .page-item a, .page-item span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: var(--radius-md);
        background-color: var(--white);
        color: var(--text);
        font-weight: var(--fw-medium);
        transition: var(--transition-base);
    }
    
    .page-item a:hover {
        background-color: var(--primary-light);
        color: var(--primary);
    }
    
    .page-item.active span {
        background-color: var(--primary);
        color: var(--white);
    }
    
    .page-item.disabled span {
        color: var(--gray-400);
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="pet-list-header">
        <h1 class="pet-list-title">Find Your Perfect Pet</h1>
        <p class="pet-list-description">Browse our selection of adorable pets looking for loving homes. Filter by category, breed, and more to find your perfect companion.</p>
    </div>
    
    <div class="pet-filters">
        <form method="get" class="filter-form">
            <div class="filter-group">
                <label for="category" class="form-label">Pet Category</label>
                <select name="category" id="category" class="form-control">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                        <option value="{{ category.name|lower }}" {% if current_category == category.name|lower %}selected{% endif %}>{{ category.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="filter-group">
                <label for="breed" class="form-label">Breed</label>
                <select name="breed" id="breed" class="form-control">
                    <option value="">All Breeds</option>
                    <!-- Breeds will be populated via AJAX based on selected category -->
                </select>
            </div>
            
            <div class="filter-group">
                <label for="adoption" class="form-label">Adoption Status</label>
                <select name="adoption" id="adoption" class="form-control">
                    <option value="">All Pets</option>
                    <option value="yes" {% if request.GET.adoption == 'yes' %}selected{% endif %}>For Adoption</option>
                </select>
            </div>
            
            <div class="filter-actions">
                <button type="submit" class="btn btn-primary">Apply Filters</button>
                <a href="{% url 'pet-list' %}" class="btn btn-outline">Clear Filters</a>
            </div>
        </form>
    </div>
    
    <div class="pet-grid">
        {% for pet in pets %}
            <div class="pet-card">
                <div class="pet-card-image">
                    <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}">
                    {% if pet.is_for_adoption %}
                        <div class="adoption-badge">For Adoption</div>
                    {% endif %}
                </div>
                <div class="pet-card-content">
                    <h3 class="pet-card-title">{{ pet.name }}</h3>
                    <p class="pet-card-breed">{{ pet.breed.name }}</p>
                    
                    <div class="pet-card-info">
                        <div class="pet-card-info-item">
                            <i class="fas fa-venus-mars"></i>
                            <span>{{ pet.get_gender_display }}</span>
                        </div>
                        
                        <div class="pet-card-info-item">
                            <i class="fas fa-birthday-cake"></i>
                            <span>{{ pet.birth_date|timesince }} old</span>
                        </div>
                    </div>
                    
                    <div class="pet-card-actions">
                        {% if pet.is_for_adoption and pet.adoption_price %}
                            <div class="pet-card-price">${{ pet.adoption_price }}</div>
                        {% else %}
                            <div></div>
                        {% endif %}
                        
                        <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-primary">View Details</a>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="empty-state">
                <p>No pets found matching your criteria.</p>
                <a href="{% url 'pet-list' %}" class="btn btn-primary">Clear Filters</a>
            </div>
        {% endfor %}
    </div>
    
    {% if is_paginated %}
        <div class="pagination-container">
            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-angle-double-left"></i></span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-angle-left"></i></span>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-angle-right"></i></span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-angle-double-right"></i></span>
                    </li>
                {% endif %}
            </ul>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const categorySelect = document.getElementById('category');
        const breedSelect = document.getElementById('breed');
        
        if (categorySelect && breedSelect) {
            categorySelect.addEventListener('change', function() {
                const category = this.value;
                
                if (category) {
                    // Fetch breeds for selected category
                    fetch(`{% url 'ajax-load-breeds' %}?category=${category}`)
                        .then(response => response.json())
                        .then(data => {
                            // Clear current options
                            breedSelect.innerHTML = '<option value="">All Breeds</option>';
                            
                            // Add new options
                            data.breeds.forEach(breed => {
                                const option = document.createElement('option');
                                option.value = breed.id;
                                option.textContent = breed.name;
                                breedSelect.appendChild(option);
                            });
                        })
                        .catch(error => console.error('Error:', error));
                } else {
                    // Reset breed select if no category selected
                    breedSelect.innerHTML = '<option value="">All Breeds</option>';
                }
            });
            
            // Trigger change event on page load if category is selected
            if (categorySelect.value) {
                categorySelect.dispatchEvent(new Event('change'));
            }
        }
    });
</script>
{% endblock %}
