/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-full);
  font-weight: var(--fw-medium);
  font-size: var(--font-base);
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  border: none;
  line-height: 1.5;
  text-decoration: none;
  font-family: var(--font-family-sans);
}

/* Ensure no underlines on any buttons, even on hover */
.btn:hover,
.btn:focus,
.btn:active {
  text-decoration: none;
}

/* Button sizes */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-base);
  font-size: var(--font-sm);
  border-radius: var(--radius-full);
}

.btn-lg {
  padding: var(--spacing-base) var(--spacing-xl);
  font-size: var(--font-lg);
  border-radius: var(--radius-full);
}

/* Button types - Fill variant */
.btn-primary {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

.btn-primary:hover {
  background-color: var(--button-primary-hover-bg);
  color: var(--button-primary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
}

.btn-secondary:hover {
  background-color: var(--button-secondary-hover-bg);
  color: var(--button-secondary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-tertiary {
  background-color: var(--tertiary);
  color: var(--white);
}

.btn-tertiary:hover {
  background-color: var(--tertiary-dark, var(--tertiary));
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-success {
  background-color: var(--success);
  color: var(--white);
}

.btn-success:hover {
  background-color: var(--success-dark, var(--success));
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-danger {
  background-color: var(--danger);
  color: var(--white);
}

.btn-danger:hover {
  background-color: var(--danger-dark, var(--danger));
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-warning {
  background-color: var(--warning);
  color: var(--dark);
}

.btn-warning:hover {
  background-color: var(--warning-dark, var(--warning));
  color: var(--dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-info {
  background-color: var(--info);
  color: var(--white);
}

.btn-info:hover {
  background-color: var(--info-dark, var(--info));
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Button variants - Outline */
.btn-outline {
  background-color: transparent;
  border: 2px solid;
}

.btn-outline-white {
  border-color: var(--white);
  color: var(--white);
}

.btn-outline-white:hover {
  background-color: var(--white);
  color: var(--text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}


.btn-primary.btn-outline {
  border-color: var(--button-primary-bg);
  color: var(--button-primary-bg);
}

.btn-primary.btn-outline:hover {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-secondary.btn-outline {
  border-color: var(--button-secondary-bg);
  color: var(--button-secondary-bg);
}

.btn-secondary.btn-outline:hover {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-tertiary.btn-outline {
  border-color: var(--tertiary);
  color: var(--tertiary);
}

.btn-tertiary.btn-outline:hover {
  background-color: var(--tertiary);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-success.btn-outline {
  border-color: var(--success);
  color: var(--success);
}

.btn-success.btn-outline:hover {
  background-color: var(--success);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-danger.btn-outline {
  border-color: var(--danger);
  color: var(--danger);
}

.btn-danger.btn-outline:hover {
  background-color: var(--danger);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-warning.btn-outline {
  border-color: var(--warning);
  color: var(--warning);
}

.btn-warning.btn-outline:hover {
  background-color: var(--warning);
  color: var(--dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.btn-info.btn-outline {
  border-color: var(--info);
  color: var(--info);
}

.btn-info.btn-outline:hover {
  background-color: var(--info);
  color: var(--white);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Button variants - Text */
.btn-text {
  background-color: transparent;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
}

.btn-primary.btn-text {
  color: var(--button-primary-bg);
}

.btn-primary.btn-text:hover {
  color: var(--button-primary-hover-bg);
  text-decoration: none;
}

.btn-secondary.btn-text {
  color: var(--button-secondary-bg);
}

.btn-secondary.btn-text:hover {
  color: var(--button-secondary-hover-bg);
  text-decoration: none;
}

.btn-tertiary.btn-text {
  color: var(--tertiary);
}

.btn-tertiary.btn-text:hover {
  color: var(--tertiary-dark, var(--tertiary));
  text-decoration: none;
}

/* Button states */
.btn:disabled, .btn.disabled {
  opacity: 0.65;
  pointer-events: none;
  cursor: not-allowed;
}

/* Legacy button classes for backward compatibility */
/* Ensure no underlines on legacy buttons */
.primary-lg-fill, .primary-lg-outline, .primary-sm-fill, .primary-sm-outline,
.primary-lg-fill:hover, .primary-lg-outline:hover, .primary-sm-fill:hover, .primary-sm-outline:hover,
.primary-lg-fill:focus, .primary-lg-outline:focus, .primary-sm-fill:focus, .primary-sm-outline:focus,
.primary-lg-fill:active, .primary-lg-outline:active, .primary-sm-fill:active, .primary-sm-outline:active {
  text-decoration: none;
}
.primary-lg-fill {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-base) var(--spacing-xl);
  border-radius: var(--radius-full);
  font-weight: var(--fw-medium);
  font-size: var(--font-lg);
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  border: none;
  line-height: 1.5;
  text-decoration: none;
  font-family: var(--font-family-sans);
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

.primary-lg-fill:hover {
  background-color: var(--button-primary-hover-bg);
  color: var(--button-primary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.primary-lg-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-base) var(--spacing-xl);
  border-radius: var(--radius-full);
  font-weight: var(--fw-medium);
  font-size: var(--font-lg);
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  border: 2px solid var(--button-primary-bg);
  line-height: 1.5;
  text-decoration: none;
  font-family: var(--font-family-sans);
  background-color: transparent;
  color: var(--button-primary-bg);
}

.primary-lg-outline:hover {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.primary-sm-fill {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-base);
  border-radius: var(--radius-full);
  font-weight: var(--fw-medium);
  font-size: var(--font-sm);
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  border: none;
  line-height: 1.5;
  text-decoration: none;
  font-family: var(--font-family-sans);
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
}

.primary-sm-fill:hover {
  background-color: var(--button-primary-hover-bg);
  color: var(--button-primary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.primary-sm-outline {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-base);
  border-radius: var(--radius-full);
  font-weight: var(--fw-medium);
  font-size: var(--font-sm);
  cursor: pointer;
  transition: var(--transition-base);
  text-align: center;
  border: 2px solid var(--button-primary-bg);
  line-height: 1.5;
  text-decoration: none;
  font-family: var(--font-family-sans);
  background-color: transparent;
  color: var(--button-primary-bg);
}

.primary-sm-outline:hover {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Cards */
.card {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-4px);
}

.card-header {
  padding: var(--spacing-base) var(--spacing-xl);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--spacing-xl);
}

.card-footer {
  padding: var(--spacing-base) var(--spacing-xl);
  border-top: 1px solid var(--gray-200);
}

/* Product Card */
.product-card {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  transition: var(--transition-base);
}

.product-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-4px);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-base);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-base);
  opacity: 0;
  transition: var(--transition-base);
}

.product-card:hover .product-actions {
  opacity: 1;
}

.product-actions button {
  background-color: var(--white);
  color: var(--text);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-base);
}

.product-actions button:hover {
  background-color: var(--primary);
  color: var(--white);
}

.product-info {
  padding: var(--spacing-base);
}

.product-info h4 {
  margin-bottom: var(--spacing-xs);
  font-size: var(--font-md);
}

.product-category {
  color: var(--text-light);
  font-size: var(--font-sm);
  margin-bottom: var(--spacing-xs);
}

.product-price {
  font-weight: var(--fw-bold);
  color: var(--primary);
  margin-bottom: var(--spacing-xs);
}

.product-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--warning);
  font-size: var(--font-sm);
}

.product-rating span {
  color: var(--text-light);
  margin-left: var(--spacing-xs);
}

/* Forms */
.form-group {
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--fw-medium);
  color: var(--text);
  font-size: var(--font-sm);
  transition: var(--transition-base);
}

/* Base Input Styles */
.form-control {
  width: 100%;
  padding: var(--spacing-base) var(--spacing-lg);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-family: var(--font-family-sans);
  font-size: var(--font-base);
  color: var(--text);
  background-color: var(--white);
  transition: var(--transition-base);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

.form-control::placeholder {
  color: var(--gray-500);
  opacity: 0.7;
}

/* Hover state */
.form-control:hover:not(:focus):not(:disabled) {
  border-color: var(--gray-400);
}

/* Disabled state */
.form-control:disabled {
  background-color: var(--gray-100);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Error state */
.form-control.is-invalid {
  border-color: var(--danger);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%23dc3545' viewBox='0 0 16 16'%3E%3Cpath d='M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  padding-right: calc(1.5em + 0.75rem);
}

.form-control.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}

/* Success state */
.form-control.is-valid {
  border-color: var(--success);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%2328a745' viewBox='0 0 16 16'%3E%3Cpath d='M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  padding-right: calc(1.5em + 0.75rem);
}

.form-control.is-valid:focus {
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

/* Input with icon */
.input-with-icon {
  position: relative;
}

.input-with-icon .form-control {
  padding-left: calc(var(--spacing-lg) + 1.5em);
}

.input-with-icon .input-icon {
  position: absolute;
  left: var(--spacing-base);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500);
  pointer-events: none;
}

/* Input sizes */
.form-control-sm {
  padding: var(--spacing-sm) var(--spacing-base);
  font-size: var(--font-sm);
  border-radius: var(--radius-sm);
}

.form-control-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-lg);
  border-radius: var(--radius-lg);
}

/* Search input */
.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-lg);
  padding-right: 40px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-full);
  font-size: var(--font-sm);
  transition: var(--transition-base);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px var(--primary-light);
}

/* Search button */
.search-button {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background-color: transparent;
  border: none;
  color: var(--gray-500);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-base);
}

.search-button:hover {
  color: var(--primary);
  background-color: var(--gray-100);
}

.search-bar {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.mobile-search {
  position: relative;
  padding: var(--spacing-base);
}

/* Textarea */
textarea.form-control {
  min-height: 100px;
  resize: vertical;
}

/* Help text */
.form-text {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: var(--font-sm);
  color: var(--text-light);
}

/* Invalid feedback */
.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: var(--spacing-xs);
  font-size: var(--font-sm);
  color: var(--danger);
}

.form-control.is-invalid ~ .invalid-feedback {
  display: block;
}

/* Checkbox and Radio */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  position: relative;
  padding-left: 1.5em;
}

.form-check-input {
  position: absolute;
  left: 0;
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: var(--white);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid var(--gray-400);
  appearance: none;
  color-adjust: exact;
  transition: var(--transition-base);
}

.form-check-input[type="checkbox"] {
  border-radius: 0.25em;
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:checked[type="checkbox"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='8' height='8' viewBox='0 0 8 8'%3E%3Cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z'/%3E%3C/svg%3E");
}

.form-check-input:checked[type="radio"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Ccircle cx='6' cy='6' r='3' fill='%23fff'/%3E%3C/svg%3E");
}

.form-check-label {
  margin-bottom: 0;
  font-size: var(--font-sm);
  cursor: pointer;
}

/* Alerts */
.alert {
  padding: var(--spacing-base) var(--spacing-xl);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
}

.alert-success {
  background-color: rgba(40, 167, 69, 0.1);
  border: 1px solid var(--success);
  color: var(--success);
}

.alert-danger {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid var(--danger);
  color: var(--danger);
}

.alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid var(--warning);
  color: var(--warning);
}

.alert-info {
  background-color: rgba(23, 162, 184, 0.1);
  border: 1px solid var(--info);
  color: var(--info);
}
